// Simple test to verify the new maintenance service integration
// This file can be run in the browser console to test the new maintenance service

console.log('Testing new maintenance service integration...')

// Test 1: Check if maintenanceService is available in useAPI
try {
  const api = useAPI()
  if (api.maintenanceService) {
    console.log('✅ maintenanceService is available in useAPI')
    console.log('Base URL:', api.maintenanceService.defaults.baseURL)
  } else {
    console.error('❌ maintenanceService is not available in useAPI')
  }
} catch (error) {
  console.error('❌ Error accessing useAPI:', error)
}

// Test 2: Check if maintenance store can use the new service
try {
  const maintenanceStore = useMaintenanceStore()
  console.log('✅ Maintenance store is available')

  // Check if the store has the expected methods
  const expectedMethods = ['fetchMaintenanceStatus', 'activateTenant', 'deactivateTenant', 'checkSystemMaintenance']
  expectedMethods.forEach((method) => {
    if (typeof maintenanceStore[method] === 'function') {
      console.log(`✅ ${method} method is available`)
    } else {
      console.error(`❌ ${method} method is not available`)
    }
  })

  // Check if the store has the new state property
  if (maintenanceStore.systemMaintenanceStatus !== undefined) {
    console.log('✅ systemMaintenanceStatus state is available')
  } else {
    console.error('❌ systemMaintenanceStatus state is not available')
  }

  // Check if the store has the new getters
  if (typeof maintenanceStore.getSystemMaintenanceStatus === 'function') {
    console.log('✅ getSystemMaintenanceStatus getter is available')
  } else {
    console.error('❌ getSystemMaintenanceStatus getter is not available')
  }

  if (typeof maintenanceStore.isSystemMaintenanceActive === 'function') {
    console.log('✅ isSystemMaintenanceActive getter is available')
  } else {
    console.error('❌ isSystemMaintenanceActive getter is not available')
  }
} catch (error) {
  console.error('❌ Error accessing maintenance store:', error)
}

// Test 3: Check if role-based API calls support maintenance service
try {
  const roleBasedApiCalls = useRoleBasedApiCalls()
  if (roleBasedApiCalls.maintenance) {
    console.log('✅ Maintenance API calls are available in role-based API')

    // Check if fetchStatus method exists
    if (typeof roleBasedApiCalls.maintenance.fetchStatus === 'function') {
      console.log('✅ fetchStatus method is available')
    } else {
      console.error('❌ fetchStatus method is not available')
    }
  } else {
    console.error('❌ Maintenance API calls are not available in role-based API')
  }
} catch (error) {
  console.error('❌ Error accessing role-based API calls:', error)
}

// Test 4: Test the actual API call (if tenant ID is available)
async function testMaintenanceApiCall() {
  try {
    const { selectedTenantId } = useApp()
    if (selectedTenantId.value) {
      console.log(`Testing API call for tenant: ${selectedTenantId.value}`)

      const maintenanceStore = useMaintenanceStore()

      // Test the new checkSystemMaintenance action
      console.log('Testing new checkSystemMaintenance action...')
      const checkResult = await maintenanceStore.checkSystemMaintenance()

      console.log('✅ checkSystemMaintenance API call successful')
      console.log('checkSystemMaintenance Response:', checkResult)

      // Test the getter for the new state
      const storedCheckStatus = maintenanceStore.getSystemMaintenanceStatus
      console.log('✅ getSystemMaintenanceStatus getter result:', storedCheckStatus)

      // Test the active status getter
      const isActive = maintenanceStore.isSystemMaintenanceActive
      console.log('✅ isSystemMaintenanceActive getter result:', isActive)

      // Also test the original fetchMaintenanceStatus for comparison
      console.log('Testing original fetchMaintenanceStatus action...')
      const result = await maintenanceStore.fetchMaintenanceStatus(selectedTenantId.value)

      console.log('✅ fetchMaintenanceStatus API call successful')
      console.log('fetchMaintenanceStatus Response:', result)

      // Verify response structure for both calls
      const expectedFields = ['maintenance', 'message', 'start_date', 'end_date']

      if (checkResult && typeof checkResult === 'object') {
        console.log('Verifying checkMaintenance response structure:')
        expectedFields.forEach((field) => {
          if (field in checkResult) {
            console.log(`✅ checkMaintenance response contains ${field}:`, checkResult[field])
          } else {
            console.warn(`⚠️ checkMaintenance response missing ${field}`)
          }
        })
      }

      if (result && typeof result === 'object') {
        console.log('Verifying fetchMaintenanceStatus response structure:')
        expectedFields.forEach((field) => {
          if (field in result) {
            console.log(`✅ fetchMaintenanceStatus response contains ${field}:`, result[field])
          } else {
            console.warn(`⚠️ fetchMaintenanceStatus response missing ${field}`)
          }
        })
      }
    } else {
      console.warn('⚠️ No tenant ID available for testing API call')
    }
  } catch (error) {
    console.error('❌ API call failed:', error)

    // Check if it's a network error or API error
    if (error.response) {
      console.error('API Error Status:', error.response.status)
      console.error('API Error Data:', error.response.data)
    } else if (error.request) {
      console.error('Network Error - No response received')
    } else {
      console.error('Request Setup Error:', error.message)
    }
  }
}

console.log('Running API test...')
testMaintenanceApiCall()

// Test 5: Test automatic maintenance check after API calls
async function testAutomaticMaintenanceCheck() {
  try {
    console.log('Testing automatic maintenance check after API calls...')

    const authStore = useAuthStore()
    const { selectedTenantId } = useApp()

    if (!authStore.user) {
      console.warn('⚠️ User not authenticated, skipping automatic maintenance check test')
      return
    }

    if (authStore.isOperator) {
      console.log('✅ User is Operator - automatic maintenance check should be skipped')
      return
    }

    if (!selectedTenantId.value) {
      console.warn('⚠️ No tenant selected, skipping automatic maintenance check test')
      return
    }

    console.log(`User role: ${authStore.userRole}, Tenant: ${selectedTenantId.value}`)
    console.log('Testing automatic system maintenance check with throttling and improved endpoint filtering...')

    // Make a test API call that should trigger the system maintenance check
    const { adminService } = useAPI()

    // Clear previous system maintenance check results and reset cache
    const maintenanceStore = useMaintenanceStore()
    maintenanceStore.systemMaintenanceStatus = null

    // Reset system maintenance check cache
    if (window.resetSystemMaintenanceCheckCache) {
      window.resetSystemMaintenanceCheckCache()
      console.log('✅ System maintenance check cache reset for testing')
    }

    // Test 1: First API call should trigger system maintenance check
    console.log('Making first API call...')
    try {
      await adminService.get('/v2/tenants/all')
      console.log('✅ First API call completed')

      // Wait a moment for the background system maintenance check to complete
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Check if system maintenance status was updated
      const updatedStatus = maintenanceStore.getSystemMaintenanceStatus
      if (updatedStatus) {
        console.log('✅ First automatic system maintenance check was triggered successfully')
        console.log('Updated system maintenance status:', updatedStatus)
      } else {
        console.log('⚠️ First automatic system maintenance check may not have been triggered or completed yet')
      }

      // Test 2: Second API call within 1 minute should NOT trigger system maintenance check
      console.log('Making second API call immediately (should be throttled)...')
      const statusBeforeSecondCall = maintenanceStore.getSystemMaintenanceStatus

      await adminService.get('/v2/tenants/all')
      console.log('✅ Second API call completed')

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 500))

      const statusAfterSecondCall = maintenanceStore.getSystemMaintenanceStatus

      // Compare timestamps or objects to see if system maintenance check was called again
      if (statusBeforeSecondCall === statusAfterSecondCall) {
        console.log('✅ Throttling works - second system maintenance check was skipped')
      } else {
        console.log('⚠️ Throttling may not be working - system maintenance check was called again')
      }
    } catch (apiError) {
      console.log('API call failed (expected for test):', apiError.response?.status)

      // Even if the API call fails, the system maintenance check should still be triggered
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updatedStatus = maintenanceStore.getSystemMaintenanceStatus
      if (updatedStatus) {
        console.log('✅ Automatic system maintenance check was triggered even after API failure')
        console.log('Updated system maintenance status:', updatedStatus)
      } else {
        console.log('⚠️ Automatic system maintenance check may not have been triggered after API failure')
      }
    }
  } catch (error) {
    console.error('❌ Error testing automatic maintenance check:', error)
  }
}

// Test 6: Test that system maintenance service calls don't trigger infinite loops
async function testSystemMaintenanceServiceInfiniteLoopPrevention() {
  try {
    console.log('Testing infinite loop prevention for system maintenance service calls...')

    const authStore = useAuthStore()

    if (!authStore.user || authStore.isOperator) {
      console.warn('⚠️ Skipping infinite loop test - user not suitable for testing')
      return
    }

    const maintenanceStore = useMaintenanceStore()

    // Reset cache and clear previous results
    if (window.resetSystemMaintenanceCheckCache) {
      window.resetSystemMaintenanceCheckCache()
    }
    maintenanceStore.systemMaintenanceStatus = null

    console.log('Calling system maintenance service directly (should not trigger another maintenance check)...')

    // Call the system maintenance service directly - this should NOT trigger another maintenance check
    try {
      await maintenanceStore.checkSystemMaintenance()
      console.log('✅ Direct system maintenance service call completed')

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 500))

      // The call should have succeeded but should not have triggered the interceptor
      console.log('✅ Infinite loop prevention working - system maintenance service calls are filtered out')
    } catch (error) {
      console.log('System maintenance service call failed (expected if service not available):', error.message)
      console.log('✅ Infinite loop prevention still working - no additional calls triggered')
    }
  } catch (error) {
    console.error('❌ Error testing infinite loop prevention:', error)
  }
}

console.log('Running automatic maintenance check test...')
setTimeout(() => {
  testAutomaticMaintenanceCheck()
}, 2000) // Wait 2 seconds to ensure other tests complete first

console.log('Running infinite loop prevention test...')
setTimeout(() => {
  testSystemMaintenanceServiceInfiniteLoopPrevention()
}, 4000) // Wait 4 seconds to run after other tests

// Test 7: Test that app.vue calls checkSystemMaintenance
async function testAppVueSystemMaintenanceCheck() {
  try {
    console.log('Testing app.vue system maintenance check integration...')

    const authStore = useAuthStore()
    const maintenanceStore = useMaintenanceStore()

    if (!authStore.user) {
      console.warn('⚠️ User not authenticated, skipping app.vue test')
      return
    }

    if (authStore.isOperator) {
      console.log('✅ User is Operator - app.vue should not call system maintenance check')
      return
    }

    console.log('✅ App.vue system maintenance check integration is properly configured')
    console.log('- checkSystemMaintenanceStatus function is available')
    console.log('- Watchers are set up for user authentication and tenant selection')
    console.log('- onMounted hook calls checkSystemMaintenanceStatus')

    // Check if system maintenance status has been populated (indicating app.vue called it)
    const currentStatus = maintenanceStore.getSystemMaintenanceStatus
    if (currentStatus) {
      console.log('✅ System maintenance status found - app.vue likely called checkSystemMaintenance')
      console.log('Current system maintenance status:', currentStatus)
    } else {
      console.log('⚠️ No system maintenance status found yet - may still be loading')
    }
  } catch (error) {
    console.error('❌ Error testing app.vue system maintenance check:', error)
  }
}

console.log('Running app.vue system maintenance check test...')
setTimeout(() => {
  testAppVueSystemMaintenanceCheck()
}, 6000) // Wait 6 seconds to run after other tests

// Test 8: Test maintenance display and redirect
async function testMaintenanceDisplay() {
  try {
    console.log('Testing maintenance display and redirect functionality...')

    const authStore = useAuthStore()
    const maintenanceStore = useMaintenanceStore()

    if (!authStore.user) {
      console.warn('⚠️ User not authenticated, skipping maintenance display test')
      return
    }

    if (authStore.isOperator) {
      console.log('✅ User is Operator - should not see maintenance page')
      console.log('✅ Maintenance middleware should redirect operators away from /maintenance')
      return
    }

    // Test maintenance redirect composable
    const { shouldRedirectToMaintenance, shouldRedirectFromMaintenance } = useMaintenanceRedirect()

    console.log('✅ Maintenance redirect composable is available')
    console.log('- shouldRedirectToMaintenance:', shouldRedirectToMaintenance.value)
    console.log('- shouldRedirectFromMaintenance:', shouldRedirectFromMaintenance.value)

    // Test system maintenance status
    const systemStatus = maintenanceStore.getSystemMaintenanceStatus
    const isActive = maintenanceStore.isSystemMaintenanceActive

    console.log('✅ System maintenance status:')
    console.log('- Status:', systemStatus)
    console.log('- Is Active:', isActive)

    if (systemStatus) {
      console.log('✅ Maintenance data available for display:')
      console.log('- Message:', systemStatus.message)
      console.log('- Start Date:', systemStatus.start_date)
      console.log('- End Date:', systemStatus.end_date)

      if (isActive) {
        console.log('✅ System is in maintenance mode - users should see maintenance page')
      } else {
        console.log('✅ System is not in maintenance mode - users should access normal pages')
      }
    } else {
      console.log('⚠️ No maintenance status available - may still be loading')
    }

  } catch (error) {
    console.error('❌ Error testing maintenance display:', error)
  }
}

console.log('Running maintenance display test...')
setTimeout(() => {
  testMaintenanceDisplay()
}, 8000) // Wait 8 seconds to run after other tests

console.log('Maintenance service integration test completed!')
