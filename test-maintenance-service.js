// Simple test to verify the new maintenance service integration
// This file can be run in the browser console to test the new maintenance service

console.log('Testing new maintenance service integration...')

// Test 1: Check if maintenanceService is available in useAPI
try {
  const api = useAPI()
  if (api.maintenanceService) {
    console.log('✅ maintenanceService is available in useAPI')
    console.log('Base URL:', api.maintenanceService.defaults.baseURL)
  } else {
    console.error('❌ maintenanceService is not available in useAPI')
  }
} catch (error) {
  console.error('❌ Error accessing useAPI:', error)
}

// Test 2: Check if maintenance store can use the new service
try {
  const maintenanceStore = useMaintenanceStore()
  console.log('✅ Maintenance store is available')

  // Check if the store has the expected methods
  const expectedMethods = ['fetchMaintenanceStatus', 'activateTenant', 'deactivateTenant', 'checkMaintenance']
  expectedMethods.forEach((method) => {
    if (typeof maintenanceStore[method] === 'function') {
      console.log(`✅ ${method} method is available`)
    } else {
      console.error(`❌ ${method} method is not available`)
    }
  })

  // Check if the store has the new state property
  if (maintenanceStore.checkMaintenanceStatus !== undefined) {
    console.log('✅ checkMaintenanceStatus state is available')
  } else {
    console.error('❌ checkMaintenanceStatus state is not available')
  }

  // Check if the store has the new getter
  if (typeof maintenanceStore.getCheckMaintenanceStatus === 'function') {
    console.log('✅ getCheckMaintenanceStatus getter is available')
  } else {
    console.error('❌ getCheckMaintenanceStatus getter is not available')
  }
} catch (error) {
  console.error('❌ Error accessing maintenance store:', error)
}

// Test 3: Check if role-based API calls support maintenance service
try {
  const roleBasedApiCalls = useRoleBasedApiCalls()
  if (roleBasedApiCalls.maintenance) {
    console.log('✅ Maintenance API calls are available in role-based API')

    // Check if fetchStatus method exists
    if (typeof roleBasedApiCalls.maintenance.fetchStatus === 'function') {
      console.log('✅ fetchStatus method is available')
    } else {
      console.error('❌ fetchStatus method is not available')
    }
  } else {
    console.error('❌ Maintenance API calls are not available in role-based API')
  }
} catch (error) {
  console.error('❌ Error accessing role-based API calls:', error)
}

// Test 4: Test the actual API call (if tenant ID is available)
async function testMaintenanceApiCall() {
  try {
    const { selectedTenantId } = useApp()
    if (selectedTenantId.value) {
      console.log(`Testing API call for tenant: ${selectedTenantId.value}`)

      const maintenanceStore = useMaintenanceStore()

      // Test the new checkMaintenance action
      console.log('Testing new checkMaintenance action...')
      const checkResult = await maintenanceStore.checkMaintenance(selectedTenantId.value)

      console.log('✅ checkMaintenance API call successful')
      console.log('checkMaintenance Response:', checkResult)

      // Test the getter for the new state
      const storedCheckStatus = maintenanceStore.getCheckMaintenanceStatus(selectedTenantId.value)
      console.log('✅ getCheckMaintenanceStatus getter result:', storedCheckStatus)

      // Also test the original fetchMaintenanceStatus for comparison
      console.log('Testing original fetchMaintenanceStatus action...')
      const result = await maintenanceStore.fetchMaintenanceStatus(selectedTenantId.value)

      console.log('✅ fetchMaintenanceStatus API call successful')
      console.log('fetchMaintenanceStatus Response:', result)

      // Verify response structure for both calls
      const expectedFields = ['maintenance', 'message', 'start_date', 'end_date']

      if (checkResult && typeof checkResult === 'object') {
        console.log('Verifying checkMaintenance response structure:')
        expectedFields.forEach((field) => {
          if (field in checkResult) {
            console.log(`✅ checkMaintenance response contains ${field}:`, checkResult[field])
          } else {
            console.warn(`⚠️ checkMaintenance response missing ${field}`)
          }
        })
      }

      if (result && typeof result === 'object') {
        console.log('Verifying fetchMaintenanceStatus response structure:')
        expectedFields.forEach((field) => {
          if (field in result) {
            console.log(`✅ fetchMaintenanceStatus response contains ${field}:`, result[field])
          } else {
            console.warn(`⚠️ fetchMaintenanceStatus response missing ${field}`)
          }
        })
      }
    } else {
      console.warn('⚠️ No tenant ID available for testing API call')
    }
  } catch (error) {
    console.error('❌ API call failed:', error)

    // Check if it's a network error or API error
    if (error.response) {
      console.error('API Error Status:', error.response.status)
      console.error('API Error Data:', error.response.data)
    } else if (error.request) {
      console.error('Network Error - No response received')
    } else {
      console.error('Request Setup Error:', error.message)
    }
  }
}

console.log('Running API test...')
testMaintenanceApiCall()

// Test 5: Test automatic maintenance check after API calls
async function testAutomaticMaintenanceCheck() {
  try {
    console.log('Testing automatic maintenance check after API calls...')

    const authStore = useAuthStore()
    const { selectedTenantId } = useApp()

    if (!authStore.user) {
      console.warn('⚠️ User not authenticated, skipping automatic maintenance check test')
      return
    }

    if (authStore.isOperator) {
      console.log('✅ User is Operator - automatic maintenance check should be skipped')
      return
    }

    if (!selectedTenantId.value) {
      console.warn('⚠️ No tenant selected, skipping automatic maintenance check test')
      return
    }

    console.log(`User role: ${authStore.userRole}, Tenant: ${selectedTenantId.value}`)
    console.log('Testing automatic maintenance check with throttling...')

    // Make a test API call that should trigger the maintenance check
    const { adminService } = useAPI()

    // Clear previous maintenance check results and reset cache
    const maintenanceStore = useMaintenanceStore()
    maintenanceStore.checkMaintenanceStatus[selectedTenantId.value] = null

    // Reset maintenance check cache for this tenant
    if (window.resetMaintenanceCheckCache) {
      window.resetMaintenanceCheckCache(selectedTenantId.value)
      console.log('✅ Maintenance check cache reset for testing')
    }

    // Test 1: First API call should trigger maintenance check
    console.log('Making first API call...')
    try {
      await adminService.get('/v2/tenants/all')
      console.log('✅ First API call completed')

      // Wait a moment for the background maintenance check to complete
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Check if maintenance status was updated
      const updatedStatus = maintenanceStore.getCheckMaintenanceStatus(selectedTenantId.value)
      if (updatedStatus) {
        console.log('✅ First automatic maintenance check was triggered successfully')
        console.log('Updated maintenance status:', updatedStatus)
      } else {
        console.log('⚠️ First automatic maintenance check may not have been triggered or completed yet')
      }

      // Test 2: Second API call within 1 minute should NOT trigger maintenance check
      console.log('Making second API call immediately (should be throttled)...')
      const statusBeforeSecondCall = maintenanceStore.getCheckMaintenanceStatus(selectedTenantId.value)

      await adminService.get('/v2/tenants/all')
      console.log('✅ Second API call completed')

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 500))

      const statusAfterSecondCall = maintenanceStore.getCheckMaintenanceStatus(selectedTenantId.value)

      // Compare timestamps or objects to see if maintenance check was called again
      if (statusBeforeSecondCall === statusAfterSecondCall) {
        console.log('✅ Throttling works - second maintenance check was skipped')
      } else {
        console.log('⚠️ Throttling may not be working - maintenance check was called again')
      }
    } catch (apiError) {
      console.log('API call failed (expected for test):', apiError.response?.status)

      // Even if the API call fails, the maintenance check should still be triggered
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updatedStatus = maintenanceStore.getCheckMaintenanceStatus(selectedTenantId.value)
      if (updatedStatus) {
        console.log('✅ Automatic maintenance check was triggered even after API failure')
        console.log('Updated maintenance status:', updatedStatus)
      } else {
        console.log('⚠️ Automatic maintenance check may not have been triggered after API failure')
      }
    }
  } catch (error) {
    console.error('❌ Error testing automatic maintenance check:', error)
  }
}

console.log('Running automatic maintenance check test...')
setTimeout(() => {
  testAutomaticMaintenanceCheck()
}, 2000) // Wait 2 seconds to ensure other tests complete first

console.log('Maintenance service integration test completed!')
