<script setup lang="ts">
import 'animate.css'
import 'driver.js/dist/driver.css'

const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const ragsStore = useRagsStore()

const { llmRagChatbot } = storeToRefs(ragsStore)
const colorMode = useColorMode()

const appConfig = useAppConfig()

const settingsStore = useSettingsStore()
const maintenanceStore = useMaintenanceStore()

const { currentColorPrimary } = storeToRefs(settingsStore)

// Maintenance redirect logic
const { setupMaintenanceWatch, checkAndRedirect } = useMaintenanceRedirect()
const color = computed(() =>
  colorMode.value === 'dark' ? '#111827' : 'white'
)

useHead({
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { key: 'theme-color', name: 'theme-color', content: color }
  ],
  link: [{ rel: 'icon', href: '/favicon.ico' }],
  htmlAttrs: {
    lang: 'en'
  },
  bodyAttrs: {
    style:
      'font-family: "游ゴシック体", YuGothic, "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック", "Yu Gothic", sans-serif;'
  }
})

const title = 'スマート公共ラボ AIコンシェルジュ'
const description = 'スマート公共ラボ AIコンシェルジュ'

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description,
  ogImage: 'https://dashboard-template.nuxt.dev/social-card.png',
  twitterImage: 'https://dashboard-template.nuxt.dev/social-card.png',
  twitterCard: 'summary_large_image'
})

// watch currentColorPrimary and update the theme color
watch(
  () => currentColorPrimary.value,
  (value) => {
    if (value) {
      appConfig.ui.primary = value
    }
  }
)

// Watch for user authentication changes and check system maintenance status
watch(
  () => user.value,
  (newUser, oldUser) => {
    // Check system maintenance when user logs in (but not when logging out)
    if (newUser && !oldUser) {
      // Small delay to ensure authentication is complete
      setTimeout(() => {
        checkSystemMaintenanceStatus()
      }, 1000)
    }
  }
)

// Watch for tenant selection changes and check system maintenance status
// Note: Since maintenance is now system-wide, we still check when tenant changes
// to ensure maintenance status is always up-to-date
watch(
  () => {
    const { selectedTenantId } = useApp()
    return selectedTenantId.value
  },
  (newTenantId, oldTenantId) => {
    // Check system maintenance when tenant changes (and user is authenticated)
    if (newTenantId && newTenantId !== oldTenantId && authStore.user) {
      checkSystemMaintenanceStatus()
    }
  }
)

// Function to check system maintenance status
const checkSystemMaintenanceStatus = async () => {
  try {
    // Only check system maintenance if user is authenticated and not an operator
    if (authStore.user && !authStore.isOperator) {
      await maintenanceStore.checkSystemMaintenance()
    }
  } catch (error) {
    console.warn('Failed to check system maintenance status in app.vue:', error)
  }
}

onMounted(() => {
  try {
    if (window?.LLMRagChatbot?.instance) {
      llmRagChatbot.value = window?.LLMRagChatbot?.instance

      // hide the chatbot bubble by default
      llmRagChatbot.value?.hideBubble()
    }
  } catch (error) {
    console.error('Error initializing chatbot:', error)
  }

  // set locale to ja
  useI18n().locale.value = 'ja'

  // Check system maintenance status on app mount
  checkSystemMaintenanceStatus()

  // Setup maintenance redirect watching
  setupMaintenanceWatch()

  // Check and redirect if needed
  checkAndRedirect()
})
</script>

<template>
  <div>
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>

    <UNotifications />
    <UModals />

    <BaseLoaderOverlay />
    <BaseConfirmModal />
  </div>
</template>
