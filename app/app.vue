<script setup lang="ts">
import 'animate.css'
import 'driver.js/dist/driver.css'

const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const ragsStore = useRagsStore()

const { llmRagChatbot } = storeToRefs(ragsStore)
const colorMode = useColorMode()

const appConfig = useAppConfig()

const settingsStore = useSettingsStore()
const maintenanceStore = useMaintenanceStore()

const { currentColorPrimary } = storeToRefs(settingsStore)
const color = computed(() =>
  colorMode.value === 'dark' ? '#111827' : 'white'
)

useHead({
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { key: 'theme-color', name: 'theme-color', content: color }
  ],
  link: [{ rel: 'icon', href: '/favicon.ico' }],
  htmlAttrs: {
    lang: 'en'
  },
  bodyAttrs: {
    style:
      'font-family: "游ゴシック体", YuGothic, "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック", "Yu Gothic", sans-serif;'
  }
})

const title = 'スマート公共ラボ AIコンシェルジュ'
const description = 'スマート公共ラボ AIコンシェルジュ'

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description,
  ogImage: 'https://dashboard-template.nuxt.dev/social-card.png',
  twitterImage: 'https://dashboard-template.nuxt.dev/social-card.png',
  twitterCard: 'summary_large_image'
})

// watch currentColorPrimary and update the theme color
watch(
  () => currentColorPrimary.value,
  (value) => {
    if (value) {
      appConfig.ui.primary = value
    }
  }
)

// Watch for user authentication changes and check maintenance status
watch(
  () => user.value,
  (newUser, oldUser) => {
    // Check maintenance when user logs in (but not when logging out)
    if (newUser && !oldUser) {
      // Small delay to ensure tenant selection is complete
      setTimeout(() => {
        checkMaintenanceStatus()
      }, 1000)
    }
  }
)

// Watch for tenant selection changes and check maintenance status
watch(
  () => {
    const { selectedTenantId } = useApp()
    return selectedTenantId.value
  },
  (newTenantId, oldTenantId) => {
    // Check maintenance when tenant changes (and user is authenticated)
    if (newTenantId && newTenantId !== oldTenantId && authStore.user) {
      checkMaintenanceStatus()
    }
  }
)

// Function to check maintenance status
const checkMaintenanceStatus = async () => {
  try {
    const { selectedTenantId } = useApp()

    // Only check maintenance if user is authenticated, not an operator, and has a selected tenant
    if (
      authStore.user &&
      !authStore.isOperator &&
      selectedTenantId.value
    ) {
      await maintenanceStore.checkMaintenance(selectedTenantId.value)
    }
  } catch (error) {
    console.warn('Failed to check maintenance status in app.vue:', error)
  }
}

onMounted(() => {
  try {
    if (window?.LLMRagChatbot?.instance) {
      llmRagChatbot.value = window?.LLMRagChatbot?.instance

      // hide the chatbot bubble by default
      llmRagChatbot.value?.hideBubble()
    }
  } catch (error) {
    console.error('Error initializing chatbot:', error)
  }

  // set locale to ja
  useI18n().locale.value = 'ja'

  // Check maintenance status on app mount
  checkMaintenanceStatus()
})
</script>

<template>
  <div>
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>

    <UNotifications />
    <UModals />

    <BaseLoaderOverlay />
    <BaseConfirmModal />
  </div>
</template>
