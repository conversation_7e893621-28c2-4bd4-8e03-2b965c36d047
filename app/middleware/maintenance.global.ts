/**
 * Global middleware to handle maintenance mode redirects
 */
export default defineNuxtRouteMiddleware((to) => {
  // Skip middleware on server side
  if (process.server) return

  const authStore = useAuthStore()
  const maintenanceStore = useMaintenanceStore()

  // Skip for logged-in operators - they should always have access
  if (authStore.user && authStore.isOperator) {
    // If operator is trying to access maintenance page, redirect them away
    if (to.path === '/maintenance') {
      return navigateTo('/')
    }
    return
  }

  // Check if system is in maintenance mode
  const isMaintenanceActive = maintenanceStore.isSystemMaintenanceActive

  // If maintenance is active and user is not on maintenance page, redirect to maintenance
  // This applies to all users (authenticated and non-authenticated) except operators
  if (isMaintenanceActive && to.path !== '/maintenance') {
    return navigateTo('/maintenance')
  }

  // If maintenance is not active and user is on maintenance page, redirect to home
  if (!isMaintenanceActive && to.path === '/maintenance') {
    return navigateTo('/')
  }
})
