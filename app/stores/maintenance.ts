import type { MaintenanceStatus, AllActiveMaintenanceResponse, MaintenanceActivationPayload } from '~/types/index.d'

export const useMaintenanceStore = defineStore('maintenanceStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    maintenanceStatus: {} as Record<string, MaintenanceStatus>,
    allActiveMaintenanceStatus: [] as MaintenanceStatus[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>
  }),
  getters: {
    /**
     * Get maintenance status for a specific tenant
     */
    getMaintenanceStatus: state => (tenantId: string): MaintenanceStatus | null => {
      return state.maintenanceStatus[tenantId] || null
    },

    /**
     * Check if a specific tenant is in maintenance mode
     */
    isMaintenanceActive: state => (tenantId: string): boolean => {
      const status = state.maintenanceStatus[tenantId]
      return status?.maintenance || false
    },

    /**
     * Get count of all active maintenance tenants
     */
    activeMaintenanceCount: (state): number => {
      return state.allActiveMaintenanceStatus.length
    }
  },
  actions: {
    /**
     * Activate maintenance mode for a specific tenant
     * Allowed: Admin or above
     * PUT /v2/maintenance/activate/tenants/{tenant_id}
     */
    async activateTenant(tenantId: string, payload: MaintenanceActivationPayload) {
      try {
        this.loadings.activateTenant = true
        this.errors.activateTenant = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.maintenance.activateTenant(tenantId, payload)

        // Update local state with API response
        this.maintenanceStatus[tenantId] = response

        return response
      } catch (error: any) {
        this.errors.activateTenant = error?.response?.data || error
        throw error
      } finally {
        this.loadings.activateTenant = false
      }
    },

    /**
     * Deactivate maintenance mode for a specific tenant
     * Allowed: Admin or above
     * PUT /v2/maintenance/deactivate/tenants/{tenant_id}
     */
    async deactivateTenant(tenantId: string) {
      try {
        this.loadings.deactivateTenant = true
        this.errors.deactivateTenant = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.maintenance.deactivateTenant(tenantId)

        // Update local state with API response or create deactivated status
        if (response && typeof response === 'object') {
          this.maintenanceStatus[tenantId] = response
        } else {
          // If no response body, create a minimal deactivated status
          this.maintenanceStatus[tenantId] = {
            tenant_id: tenantId,
            maintenance: false,
            message: '',
            start_date: '',
            end_date: ''
          }
        }

        return response
      } catch (error: any) {
        this.errors.deactivateTenant = error?.response?.data || error
        throw error
      } finally {
        this.loadings.deactivateTenant = false
      }
    },

    /**
     * Activate maintenance mode for all tenants
     * Allowed: Operator only
     * PUT /v2/maintenance/activate/all
     */
    async activateAll(payload: MaintenanceActivationPayload) {
      try {
        this.loadings.activateAll = true
        this.errors.activateAll = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.maintenance.activateAll(payload)

        // Refresh all active maintenance status after activation
        await this.fetchAllActive()

        return response
      } catch (error: any) {
        this.errors.activateAll = error?.response?.data || error
        throw error
      } finally {
        this.loadings.activateAll = false
      }
    },

    /**
     * Deactivate maintenance mode for all tenants
     * Allowed: Operator only
     * PUT /v2/maintenance/deactivate/all
     */
    async deactivateAll() {
      try {
        this.loadings.deactivateAll = true
        this.errors.deactivateAll = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.maintenance.deactivateAll()

        // Clear all maintenance status after deactivation
        this.maintenanceStatus = {}
        this.allActiveMaintenanceStatus = []

        return response
      } catch (error: any) {
        this.errors.deactivateAll = error?.response?.data || error
        throw error
      } finally {
        this.loadings.deactivateAll = false
      }
    },

    /**
     * Get maintenance status of specific tenant
     * Allowed: Staff or above
     * GET /v2/maintenance/tenants/{tenant_id}
     */
    async fetchMaintenanceStatus(tenantId: string) {
      try {
        this.loadings.fetchMaintenanceStatus = true
        this.errors.fetchMaintenanceStatus = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.maintenance.fetchStatus(tenantId)

        // Update local state
        this.maintenanceStatus[tenantId] = response

        return response
      } catch (error: any) {
        this.errors.fetchMaintenanceStatus = error?.response?.data || error
        throw error
      } finally {
        this.loadings.fetchMaintenanceStatus = false
      }
    },

    /**
     * Get all active maintenance status
     * Allowed: Operator only
     * GET /v2/maintenance/active
     */
    async fetchAllActive() {
      try {
        this.loadings.fetchAllActive = true
        this.errors.fetchAllActive = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const data: AllActiveMaintenanceResponse = await roleBasedApiCalls.maintenance.fetchAllActive()

        // Update local state
        this.allActiveMaintenanceStatus = data.tenants || []

        // Also update individual maintenance status for each active tenant
        data.tenants?.forEach((status: MaintenanceStatus) => {
          this.maintenanceStatus[status.tenant_id] = status
        })

        return data
      } catch (error: any) {
        this.errors.fetchAllActive = error?.response?.data || error
        throw error
      } finally {
        this.loadings.fetchAllActive = false
      }
    },

    /**
     * Toggle maintenance mode for a specific tenant
     * Convenience method that activates or deactivates based on current status
     */
    async toggleTenantMaintenance(tenantId: string, activationPayload?: MaintenanceActivationPayload) {
      const currentStatus = this.getMaintenanceStatus(tenantId)
      const isCurrentlyActive = currentStatus?.maintenance || false

      if (isCurrentlyActive) {
        return await this.deactivateTenant(tenantId)
      } else {
        // Use provided payload or create default indefinite maintenance
        const payload = activationPayload || {
          message: 'メンテナンスモード'
          // No start_date and end_date for indefinite maintenance
        }
        return await this.activateTenant(tenantId, payload)
      }
    },

    /**
     * Clear all errors
     */
    clearErrors() {
      this.errors = {}
    },

    /**
     * Clear specific error
     */
    clearError(errorKey: string) {
      this.errors[errorKey] = null
    },

    /**
     * Format date to YYYY-MM-DD HH:mm:ss format
     */
    formatDateTime(date: Date): string {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
})
