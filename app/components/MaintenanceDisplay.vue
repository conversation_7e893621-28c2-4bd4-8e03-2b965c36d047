<script setup lang="ts">
interface Props {
  message?: string
  startDate?: string
  endDate?: string
  showCountdown?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  message: 'システムメンテナンス中です',
  showCountdown: true
})

// Countdown logic
const countdown = ref('')
const isMaintenanceActive = ref(true)

const updateCountdown = () => {
  if (!props.endDate) {
    countdown.value = ''
    return
  }

  const now = new Date().getTime()
  const endTime = new Date(props.endDate).getTime()
  const distance = endTime - now

  if (distance < 0) {
    countdown.value = ''
    isMaintenanceActive.value = false
    return
  }

  const days = Math.floor(distance / (1000 * 60 * 60 * 24))
  const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((distance % (1000 * 60)) / 1000)

  if (days > 0) {
    countdown.value = `${days}日 ${hours}時間 ${minutes}分 ${seconds}秒`
  } else if (hours > 0) {
    countdown.value = `${hours}時間 ${minutes}分 ${seconds}秒`
  } else if (minutes > 0) {
    countdown.value = `${minutes}分 ${seconds}秒`
  } else {
    countdown.value = `${seconds}秒`
  }
}

// Update countdown every second
let countdownInterval: NodeJS.Timeout | null = null

onMounted(() => {
  if (props.showCountdown && props.endDate) {
    updateCountdown()
    countdownInterval = setInterval(updateCountdown, 1000)
  }
})

onUnmounted(() => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }
})

// Format dates for display
const formatDate = (dateString?: string) => {
  if (!dateString) return ''

  try {
    const date = new Date(dateString)
    return date.toLocaleString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Tokyo'
    })
  } catch (error) {
    return dateString
  }
}
</script>

<template>
  <div class="maintenance-container">
    <!-- Maintenance Icon -->
    <div class="maintenance-icon">
      <UIcon
        name="i-heroicons-wrench-screwdriver"
        class="w-24 h-24 text-primary-500 animate-pulse"
      />
    </div>

    <!-- Main Content -->
    <div class="maintenance-content">
      <h1 class="maintenance-title">
        システムメンテナンス中
      </h1>

      <div class="maintenance-message">
        <p class="text-lg text-gray-800 dark:text-gray-300 leading-relaxed">
          {{ message }}
        </p>
      </div>

      <!-- Countdown Timer -->
      <div v-if="showCountdown && endDate && isMaintenanceActive" class="countdown-section">
        <div class="countdown-label">
          <UIcon name="i-heroicons-clock" class="w-5 h-5" />
          <span>メンテナンス終了予定時刻まで</span>
        </div>

        <div class="countdown-timer">
          {{ countdown }}
        </div>

        <div class="countdown-end-time">
          終了予定: {{ formatDate(endDate) }} (日本時間)
        </div>
      </div>

      <!-- Start Time Display -->
      <div v-if="startDate" class="maintenance-schedule">
        <div class="schedule-item">
          <UIcon name="i-heroicons-calendar" class="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <span class="text-sm text-gray-700 dark:text-gray-400">
            開始時刻: {{ formatDate(startDate) }} (日本時間)
          </span>
        </div>
      </div>

      <!-- Maintenance completed message -->
      <div v-if="showCountdown && endDate && !isMaintenanceActive" class="maintenance-completed">
        <UIcon name="i-heroicons-check-circle" class="w-6 h-6 text-green-500" />
        <span class="text-green-600 dark:text-green-400 font-medium">
          メンテナンスが完了しました
        </span>
      </div>

      <!-- Contact Information -->
      <div class="maintenance-footer">
        <p class="text-sm text-gray-700 dark:text-gray-400">
          ご不便をおかけして申し訳ございません。<br>
          お急ぎの場合は、サポートまでお問い合わせください。
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.maintenance-container {
  @apply flex flex-col items-center justify-center min-h-screen p-8 text-center;
  max-width: 600px;
  margin: 0 auto;
}

.maintenance-icon {
  @apply mb-8;
}

.maintenance-content {
  @apply space-y-6 w-full;
}

.maintenance-title {
  @apply text-4xl font-bold text-gray-800 dark:text-gray-100 mb-4;
}

.maintenance-message {
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-200 dark:border-gray-700;
}

.countdown-section {
  @apply bg-primary-50 dark:bg-primary-900/20 rounded-lg p-6 border border-primary-200 dark:border-primary-800;
}

.countdown-label {
  @apply flex items-center justify-center gap-2 text-primary-700 dark:text-primary-300 font-medium mb-3;
}

.countdown-timer {
  @apply text-3xl font-mono font-bold text-primary-600 dark:text-primary-400 mb-2;
}

.countdown-end-time {
  @apply text-sm text-primary-600 dark:text-primary-400;
}

.maintenance-schedule {
  @apply space-y-2;
}

.schedule-item {
  @apply flex items-center justify-center gap-2;
}

.maintenance-completed {
  @apply flex items-center justify-center gap-2 bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800;
}

.maintenance-footer {
  @apply bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700;
}

/* Animation for the maintenance icon */
@keyframes maintenance-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.maintenance-icon .animate-pulse {
  animation: maintenance-pulse 2s ease-in-out infinite;
}
</style>
