/**
 * Composable to handle maintenance mode redirects
 */
export const useMaintenanceRedirect = () => {
  const maintenanceStore = useMaintenanceStore()
  const authStore = useAuthStore()
  const router = useRouter()
  const route = useRoute()

  /**
   * Check if current user should be redirected to maintenance page
   */
  const shouldRedirectToMaintenance = computed(() => {
    // Don't redirect operators
    if (authStore.isOperator) {
      return false
    }

    // Don't redirect if user is not authenticated
    if (!authStore.user) {
      return false
    }

    // Don't redirect if already on maintenance page
    if (route.path === '/maintenance') {
      return false
    }

    // Check if system is in maintenance mode
    return maintenanceStore.isSystemMaintenanceActive
  })

  /**
   * Check if current user should be redirected away from maintenance page
   */
  const shouldRedirectFromMaintenance = computed(() => {
    // Redirect operators away from maintenance page
    if (authStore.isOperator && route.path === '/maintenance') {
      return true
    }

    // Redirect if maintenance is not active and user is on maintenance page
    if (!maintenanceStore.isSystemMaintenanceActive && route.path === '/maintenance') {
      return true
    }

    return false
  })

  /**
   * Perform maintenance redirect if needed
   */
  const handleMaintenanceRedirect = () => {
    if (shouldRedirectToMaintenance.value) {
      router.push('/maintenance')
    } else if (shouldRedirectFromMaintenance.value) {
      router.push('/')
    }
  }

  /**
   * Set up automatic maintenance redirect watching
   */
  const setupMaintenanceWatch = () => {
    // Watch for maintenance status changes
    watch(
      () => maintenanceStore.isSystemMaintenanceActive,
      () => {
        nextTick(() => {
          handleMaintenanceRedirect()
        })
      }
    )

    // Watch for user role changes
    watch(
      () => authStore.isOperator,
      () => {
        nextTick(() => {
          handleMaintenanceRedirect()
        })
      }
    )

    // Watch for route changes
    watch(
      () => route.path,
      () => {
        nextTick(() => {
          handleMaintenanceRedirect()
        })
      }
    )
  }

  /**
   * Check maintenance status and redirect immediately if needed
   */
  const checkAndRedirect = async () => {
    try {
      // Only check if user is not an operator
      if (!authStore.isOperator && authStore.user) {
        await maintenanceStore.checkSystemMaintenance()
      }
      
      // Handle redirect after status check
      handleMaintenanceRedirect()
    } catch (error) {
      console.warn('Failed to check maintenance status for redirect:', error)
    }
  }

  return {
    shouldRedirectToMaintenance: readonly(shouldRedirectToMaintenance),
    shouldRedirectFromMaintenance: readonly(shouldRedirectFromMaintenance),
    handleMaintenanceRedirect,
    setupMaintenanceWatch,
    checkAndRedirect
  }
}
